// vite.config.ts
import path from "node:path";
import process from "node:process";
import VueI18nPlugin from "file:///D:/project/vue/new/alone_frontend/node_modules/.pnpm/@intlify+unplugin-vue-i18n@_d58c6605d310b38ba138dca8fb9ecaef/node_modules/@intlify/unplugin-vue-i18n/lib/vite.mjs";
import { getProxy } from "file:///D:/project/vue/new/alone_frontend/node_modules/.pnpm/@peng_kai+kit@0.3.0-beta.8__a6094210239f148ea7786719c7ecf1cb/node_modules/@peng_kai/kit/vite/index.mjs";
import vue from "file:///D:/project/vue/new/alone_frontend/node_modules/.pnpm/@vitejs+plugin-vue@5.2.4_vi_8a820669c73f52982a96d86974773f7f/node_modules/@vitejs/plugin-vue/dist/index.mjs";
import vueJsx from "file:///D:/project/vue/new/alone_frontend/node_modules/.pnpm/@vitejs+plugin-vue-jsx@4.2._e0e088fe15b63151b4772521257d1516/node_modules/@vitejs/plugin-vue-jsx/dist/index.mjs";
import autoprefixer from "file:///D:/project/vue/new/alone_frontend/node_modules/.pnpm/autoprefixer@10.4.21_postcss@8.5.3/node_modules/autoprefixer/lib/autoprefixer.js";
import postcssPresetEnv from "file:///D:/project/vue/new/alone_frontend/node_modules/.pnpm/postcss-preset-env@10.1.6_postcss@8.5.3/node_modules/postcss-preset-env/dist/index.mjs";
import externalGlobals from "file:///D:/project/vue/new/alone_frontend/node_modules/.pnpm/rollup-plugin-external-globals@0.12.1_rollup@4.41.0/node_modules/rollup-plugin-external-globals/index.js";
import { visualizer } from "file:///D:/project/vue/new/alone_frontend/node_modules/.pnpm/rollup-plugin-visualizer@5.14.0_rollup@4.41.0/node_modules/rollup-plugin-visualizer/dist/plugin/index.js";
import UnoCSS from "file:///D:/project/vue/new/alone_frontend/node_modules/.pnpm/unocss@0.63.4_postcss@8.5.3_6478cff64411174942c63c10ce5ecf03/node_modules/unocss/dist/vite.mjs";
import AutoImport from "file:///D:/project/vue/new/alone_frontend/node_modules/.pnpm/unplugin-auto-import@0.18.6_040530ab3e69b4e12e08cdc29e79d5b9/node_modules/unplugin-auto-import/dist/vite.js";
import { AntDesignVueResolver } from "file:///D:/project/vue/new/alone_frontend/node_modules/.pnpm/unplugin-vue-components@0.2_4a74c09ce21fdaa5b7b9fbf17d44bb79/node_modules/unplugin-vue-components/dist/resolvers.js";
import Components from "file:///D:/project/vue/new/alone_frontend/node_modules/.pnpm/unplugin-vue-components@0.2_4a74c09ce21fdaa5b7b9fbf17d44bb79/node_modules/unplugin-vue-components/dist/vite.js";
import { defineConfig, loadEnv } from "file:///D:/project/vue/new/alone_frontend/node_modules/.pnpm/vite@5.4.19_@types+node@20.17.50_sass@1.89.0_terser@5.40.0/node_modules/vite/dist/node/index.js";
import { createHtmlPlugin } from "file:///D:/project/vue/new/alone_frontend/node_modules/.pnpm/vite-plugin-html@3.2.2_vite_12317e92efde87b3d450b166b7c9135e/node_modules/vite-plugin-html/dist/index.mjs";
import tsAlias from "file:///D:/project/vue/new/alone_frontend/node_modules/.pnpm/vite-plugin-ts-alias@0.1.1/node_modules/vite-plugin-ts-alias/dist/index.mjs";
import VueDevTools from "file:///D:/project/vue/new/alone_frontend/node_modules/.pnpm/vite-plugin-vue-devtools@7._2e539be0c38f444375fd9decf93c7eb1/node_modules/vite-plugin-vue-devtools/dist/vite.mjs";
var ENV_DIR = "./envs";
var vite_config_default = defineConfig(({ mode }) => {
  const env = loadEnv(mode, ENV_DIR);
  const proxys = getProxy(env);
  const isProd = mode === "production";
  const isStg = mode === "staging";
  const isDev = mode === "development";
  return {
    envDir: ENV_DIR,
    define: {
      "__APP_VERSION__": `'${process.env.npm_package_version}'`,
      "import.meta.env.VERSION": `"${Date.now()}"`
    },
    server: {
      host: true,
      port: Number(env.VITE_PORT),
      proxy: {
        ...proxys,
        "^/g/(.+)/(api|ws)": {
          target: "https://img.g9aaa.com",
          // target: 'ws://*************:9980',
          changeOrigin: true,
          ws: true
        }
      }
    },
    build: {
      target: ["chrome70", "safari14", "firefox68"],
      reportCompressedSize: true,
      minify: "terser",
      // sourcemap: 'hidden',
      terserOptions: {
        compress: {
          drop_console: env.VITE_DROP_CONSOLE === "1",
          drop_debugger: true
        }
      },
      rollupOptions: {
        external: ["echarts", "lottie-web", "@tonconnect/ui"],
        plugins: [
          externalGlobals({
            // 通过 CDN 引入的 ECharts，看index.html中的引入
            "echarts": "echarts",
            "lottie-web": "lottie",
            "@tonconnect/ui": "TON_CONNECT_UI"
          })
        ],
        input: {
          main: path.resolve("index.html"),
          agent: path.resolve("agent.html")
        },
        onwarn: (warning, warn) => {
          if (["CIRCULAR_DEPENDENCY", "CYCLIC_CROSS_CHUNK_REEXPORT"].includes(warning.code)) {
            return;
          }
          warn(warning);
        }
      }
    },
    plugins: [
      vue({
        template: {
          compilerOptions: {
            isCustomElement: (tag) => tag.startsWith("swiper-")
          }
        }
      }),
      vueJsx(),
      UnoCSS({ inspector: true }),
      globalDefinePlugin(),
      visualizer(),
      VueDevTools(),
      // splitVendorChunkPlugin(),
      tsAlias({ tsConfigName: "tsconfig.app.json" }),
      AutoImport({
        dts: "src/types/auto-imports.d.ts",
        vueTemplate: true,
        imports: ["vue", "vue-router"],
        dirs: [path.resolve("src/auto-import")]
      }),
      Components({
        dts: "src/types/components.d.ts",
        dirs: [],
        globs: [],
        resolvers: [
          // Antd 组件（AXxx）
          AntDesignVueResolver({ importStyle: false }),
          // 本项目组件（TXxx）
          {
            type: "component",
            resolve: (name) => name.match(/^T[A-Z]/) && { name: name.replace(/^T/, ""), from: "~/auto-import/components" }
          }
          // {
          //   type: 'directive' as const,
          //   resolve: (name) => name.match(/^D[A-Z]/) && { name, from: '~/auto-import/directives' },
          // }
        ]
      }),
      VueI18nPlugin({
        runtimeOnly: true,
        compositionOnly: true,
        fullInstall: true,
        include: [path.resolve("src/modules/locale/**/appMessage.json")]
      }),
      createHtmlPlugin({
        inject: {
          tags: [
            ...!isProd ? getErudaCode() : []
          ]
        }
      })
      // sentryVitePlugin({
      //   org: 'ma-sy',
      //   project: 'gam',
      //   authToken: '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',
      // }),
    ],
    css: {
      preprocessorOptions: {
        scss: {
          api: "modern-compiler"
        }
      },
      postcss: {
        plugins: [
          autoprefixer(),
          postcssPresetEnv()
        ]
      }
    }
  };
});
function getErudaCode() {
  return [
    { tag: "script", attrs: { src: "//cdn.jsdelivr.net/npm/eruda" }, injectTo: "head-prepend" },
    { tag: "script", children: "/Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent) && eruda.init()", injectTo: "body" }
  ];
}
function globalDefinePlugin() {
  let variables = {};
  return {
    name: "vite-plugin-global-define",
    apply: "build",
    configResolved(resolvedConfig) {
      const define = resolvedConfig.define;
      variables = Object.entries(define ?? {}).filter(([key]) => key.startsWith("__APP_")).reduce((acc, [key, value]) => {
        acc[key] = value;
        return acc;
      }, {});
    },
    transformIndexHtml(html) {
      const content = Object.entries(variables).map(([k, v]) => `window.${k} = ${v}`).join(";");
      return html.replace("<head>", `<head>
<script>${content};</script>`);
    }
  };
}
export {
  vite_config_default as default
};
//# sourceMappingURL=data:application/json;base64,****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************
